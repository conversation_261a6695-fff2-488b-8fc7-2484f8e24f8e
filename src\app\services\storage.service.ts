import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class StorageService {

  // Storage keys
  private readonly STORAGE_KEYS = {
    USERS: 'library_users',
    BOOKS: 'library_books',
    TRANSACTIONS: 'library_transactions',
    CURRENT_USER: 'currentUser'
  };

  constructor() { }

  // Clear all library data from localStorage
  clearAllData(): void {
    if (typeof localStorage !== 'undefined') {
      try {
        Object.values(this.STORAGE_KEYS).forEach(key => {
          localStorage.removeItem(key);
        });
        console.log('All library data cleared from localStorage');
      } catch (error) {
        console.error('Error clearing localStorage:', error);
      }
    }
  }

  // Reset to default data (useful for development/testing)
  resetToDefaults(): void {
    this.clearAllData();
    // The services will automatically load default data on next initialization
    window.location.reload();
  }

  // Get storage usage info
  getStorageInfo(): { [key: string]: any } {
    const info: { [key: string]: any } = {};

    if (typeof localStorage !== 'undefined') {
      Object.entries(this.STORAGE_KEYS).forEach(([name, key]) => {
        const data = localStorage.getItem(key);
        info[name] = {
          exists: !!data,
          size: data ? data.length : 0,
          itemCount: data ? this.getItemCount(data) : 0
        };
      });
    }

    return info;
  }

  private getItemCount(jsonString: string): number {
    try {
      const parsed = JSON.parse(jsonString);
      return Array.isArray(parsed) ? parsed.length : 1;
    } catch {
      return 0;
    }
  }

  // Export all data as JSON (for backup)
  exportData(): string {
    const exportData: { [key: string]: any } = {};

    if (typeof localStorage !== 'undefined') {
      Object.entries(this.STORAGE_KEYS).forEach(([name, key]) => {
        const data = localStorage.getItem(key);
        if (data) {
          try {
            exportData[name] = JSON.parse(data);
          } catch (error) {
            console.error(`Error parsing ${name} data:`, error);
          }
        }
      });
    }

    return JSON.stringify(exportData, null, 2);
  }

  // Import data from JSON (for restore)
  importData(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData);

      // Validate data structure
      if (!this.validateImportData(data)) {
        console.error('Invalid data structure for import');
        return false;
      }

      if (typeof localStorage !== 'undefined') {
        Object.entries(this.STORAGE_KEYS).forEach(([name, key]) => {
          if (data[name]) {
            localStorage.setItem(key, JSON.stringify(data[name]));
          }
        });
      }

      console.log('Data imported successfully');
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      return false;
    }
  }

  // Validate imported data structure
  private validateImportData(data: any): boolean {
    if (!data || typeof data !== 'object') return false;

    // Check if data has expected structure
    const expectedKeys = ['USERS', 'BOOKS', 'TRANSACTIONS'];
    return expectedKeys.some(key => data[key] !== undefined);
  }

  // Safe get item with error handling
  safeGetItem(key: string): any | null {
    if (typeof localStorage === 'undefined') return null;

    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error(`Error parsing localStorage item ${key}:`, error);
      // Remove corrupted data
      localStorage.removeItem(key);
      return null;
    }
  }

  // Safe set item with error handling
  safeSetItem(key: string, value: any): boolean {
    if (typeof localStorage === 'undefined') return false;

    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error(`Error saving to localStorage ${key}:`, error);
      return false;
    }
  }

  // Check localStorage availability and quota
  checkStorageHealth(): { available: boolean; quota?: number; used?: number; error?: string } {
    if (typeof localStorage === 'undefined') {
      return { available: false, error: 'localStorage not available' };
    }

    try {
      // Test write capability
      const testKey = '__storage_test__';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);

      // Estimate usage (rough calculation)
      let used = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }

      return { available: true, used };
    } catch (error) {
      return { available: false, error: (error as Error).message };
    }
  }
}
