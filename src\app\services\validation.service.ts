import { Injectable } from '@angular/core';
import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

@Injectable({
  providedIn: 'root'
})
export class ValidationService {

  constructor() { }

  // Custom ISBN validator
  static isbnValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null; // Let required validator handle empty values
      }

      const isbn = control.value.replace(/[-\s]/g, ''); // Remove hyphens and spaces
      
      // Check if it's a valid ISBN-10 or ISBN-13
      if (isbn.length === 10) {
        return ValidationService.validateISBN10(isbn) ? null : { invalidIsbn: true };
      } else if (isbn.length === 13) {
        return ValidationService.validateISBN13(isbn) ? null : { invalidIsbn: true };
      } else {
        return { invalidIsbn: true };
      }
    };
  }

  // Validate ISBN-10
  private static validateISBN10(isbn: string): boolean {
    if (!/^\d{9}[\dX]$/.test(isbn)) {
      return false;
    }

    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(isbn[i]) * (10 - i);
    }

    const checkDigit = isbn[9] === 'X' ? 10 : parseInt(isbn[9]);
    sum += checkDigit;

    return sum % 11 === 0;
  }

  // Validate ISBN-13
  private static validateISBN13(isbn: string): boolean {
    if (!/^\d{13}$/.test(isbn)) {
      return false;
    }

    let sum = 0;
    for (let i = 0; i < 12; i++) {
      sum += parseInt(isbn[i]) * (i % 2 === 0 ? 1 : 3);
    }

    const checkDigit = parseInt(isbn[12]);
    const calculatedCheckDigit = (10 - (sum % 10)) % 10;

    return checkDigit === calculatedCheckDigit;
  }

  // Email validator (more comprehensive than built-in)
  static emailValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return emailRegex.test(control.value) ? null : { invalidEmail: true };
    };
  }

  // Password strength validator
  static passwordStrengthValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const password = control.value;
      const errors: ValidationErrors = {};

      if (password.length < 8) {
        errors['minLength'] = true;
      }

      if (!/[A-Z]/.test(password)) {
        errors['requiresUppercase'] = true;
      }

      if (!/[a-z]/.test(password)) {
        errors['requiresLowercase'] = true;
      }

      if (!/\d/.test(password)) {
        errors['requiresNumber'] = true;
      }

      return Object.keys(errors).length > 0 ? { passwordStrength: errors } : null;
    };
  }

  // Date range validator
  static dateRangeValidator(minDate?: Date, maxDate?: Date): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const date = new Date(control.value);
      const errors: ValidationErrors = {};

      if (minDate && date < minDate) {
        errors['minDate'] = { actualDate: date, minDate: minDate };
      }

      if (maxDate && date > maxDate) {
        errors['maxDate'] = { actualDate: date, maxDate: maxDate };
      }

      return Object.keys(errors).length > 0 ? errors : null;
    };
  }

  // No whitespace validator
  static noWhitespaceValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const isWhitespace = (control.value || '').trim().length === 0;
      return isWhitespace ? { whitespace: true } : null;
    };
  }

  // Get error message for a form control
  static getErrorMessage(control: AbstractControl, fieldName: string): string {
    if (control.errors) {
      if (control.errors['required']) {
        return `${fieldName} is required`;
      }
      if (control.errors['minlength']) {
        return `${fieldName} must be at least ${control.errors['minlength'].requiredLength} characters`;
      }
      if (control.errors['maxlength']) {
        return `${fieldName} cannot exceed ${control.errors['maxlength'].requiredLength} characters`;
      }
      if (control.errors['min']) {
        return `${fieldName} must be at least ${control.errors['min'].min}`;
      }
      if (control.errors['max']) {
        return `${fieldName} cannot exceed ${control.errors['max'].max}`;
      }
      if (control.errors['email'] || control.errors['invalidEmail']) {
        return `Please enter a valid email address`;
      }
      if (control.errors['invalidIsbn']) {
        return `Please enter a valid ISBN-10 or ISBN-13`;
      }
      if (control.errors['pattern']) {
        return `${fieldName} format is invalid`;
      }
      if (control.errors['whitespace']) {
        return `${fieldName} cannot be empty or contain only whitespace`;
      }
      if (control.errors['passwordStrength']) {
        const errors = control.errors['passwordStrength'];
        const messages = [];
        if (errors['minLength']) messages.push('at least 8 characters');
        if (errors['requiresUppercase']) messages.push('an uppercase letter');
        if (errors['requiresLowercase']) messages.push('a lowercase letter');
        if (errors['requiresNumber']) messages.push('a number');
        return `Password must contain ${messages.join(', ')}`;
      }
      if (control.errors['minDate']) {
        return `Date must be after ${control.errors['minDate'].minDate.toLocaleDateString()}`;
      }
      if (control.errors['maxDate']) {
        return `Date must be before ${control.errors['maxDate'].maxDate.toLocaleDateString()}`;
      }
    }
    return '';
  }

  // Check if form has any errors
  static hasErrors(control: AbstractControl): boolean {
    return control.invalid && (control.dirty || control.touched);
  }

  // Mark all fields as touched to show validation errors
  static markFormGroupTouched(formGroup: any): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control && typeof control === 'object' && control.controls) {
        ValidationService.markFormGroupTouched(control);
      }
    });
  }
}
