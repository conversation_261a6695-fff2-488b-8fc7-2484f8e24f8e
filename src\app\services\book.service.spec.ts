import { TestBed } from '@angular/core/testing';
import { Book, BookGenre, CreateBookRequest, UpdateBookRequest } from '../models';
import { BookService } from './book.service';

describe('BookService', () => {
  let service: BookService;
  let mockLocalStorage: { [key: string]: string };

  beforeEach(() => {
    // Mock localStorage
    mockLocalStorage = {};
    spyOn(localStorage, 'getItem').and.callFake((key: string) => mockLocalStorage[key] || null);
    spyOn(localStorage, 'setItem').and.callFake((key: string, value: string) => {
      mockLocalStorage[key] = value;
    });
    spyOn(localStorage, 'removeItem').and.callFake((key: string) => {
      delete mockLocalStorage[key];
    });

    TestBed.configureTestingModule({});
    service = TestBed.inject(BookService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should load default books on initialization', () => {
    service.getAllBooks().subscribe(books => {
      expect(books.length).toBeGreaterThan(0);
      expect(books[0].id).toBeDefined();
      expect(books[0].title).toBeDefined();
      expect(books[0].author).toBeDefined();
    });
  });

  it('should add a new book', () => {
    const newBookData: CreateBookRequest = {
      title: 'Test Book',
      author: 'Test Author',
      isbn: '978-0-123456-78-9',
      genre: BookGenre.FICTION,
      description: 'A test book for unit testing',
      totalCopies: 5,
      publishedDate: new Date('2023-01-01')
    };

    service.addBook(newBookData).subscribe(newBook => {
      expect(newBook.title).toBe(newBookData.title);
      expect(newBook.author).toBe(newBookData.author);
      expect(newBook.availableCopies).toBe(newBookData.totalCopies);
      expect(newBook.isActive).toBe(true);
    });
  });

  it('should update book availability when borrowing', () => {
    const bookId = '1';
    const initialAvailableCopies = 5;

    service.updateBookAvailability(bookId, -1).subscribe(success => {
      expect(success).toBe(true);
    });
  });

  it('should update book availability when returning', () => {
    const bookId = '1';

    service.updateBookAvailability(bookId, 1).subscribe(success => {
      expect(success).toBe(true);
    });
  });

  it('should update an existing book', () => {
    const bookId = '1';
    const updateData: UpdateBookRequest = {
      title: 'Updated Title',
      totalCopies: 10
    };

    service.updateBook(bookId, updateData).subscribe(updatedBook => {
      expect(updatedBook).toBeTruthy();
      if (updatedBook) {
        expect(updatedBook.title).toBe(updateData.title!);
        expect(updatedBook.totalCopies).toBe(updateData.totalCopies!);
      }
    });
  });

  it('should filter available books only', () => {
    service.getAvailableBooks().subscribe(books => {
      books.forEach(book => {
        expect(book.isActive).toBe(true);
        expect(book.availableCopies).toBeGreaterThan(0);
      });
    });
  });

  it('should search books by criteria', () => {
    const searchCriteria = {
      title: 'Harry',
      author: '',
      genre: '',
      isAvailable: true
    };

    service.searchBooks(searchCriteria).subscribe(books => {
      books.forEach(book => {
        expect(book.title.toLowerCase()).toContain('harry');
      });
    });
  });

  it('should delete a book (mark as inactive)', () => {
    const bookId = '1';

    service.deleteBook(bookId).subscribe(success => {
      expect(success).toBe(true);
    });
  });

  it('should persist books to localStorage', () => {
    const newBookData: CreateBookRequest = {
      title: 'Persistence Test',
      author: 'Test Author',
      isbn: '978-0-123456-78-0',
      genre: BookGenre.SCIENCE,
      description: 'Testing persistence',
      totalCopies: 3,
      publishedDate: new Date('2023-01-01')
    };

    service.addBook(newBookData).subscribe(() => {
      expect(localStorage.setItem).toHaveBeenCalledWith('library_books', jasmine.any(String));
    });
  });

  it('should emit book updates through observable', () => {
    let emittedBooks: Book[] = [];

    service.books$.subscribe(books => {
      emittedBooks = books;
    });

    const newBookData: CreateBookRequest = {
      title: 'Observable Test',
      author: 'Test Author',
      isbn: '978-0-123456-78-1',
      genre: BookGenre.MYSTERY,
      description: 'Testing observables',
      totalCopies: 2,
      publishedDate: new Date('2023-01-01')
    };

    service.addBook(newBookData).subscribe(() => {
      expect(emittedBooks.length).toBeGreaterThan(0);
      expect(emittedBooks.some(book => book.title === 'Observable Test')).toBe(true);
    });
  });
});
