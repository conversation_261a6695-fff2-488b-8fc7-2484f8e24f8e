import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { BorrowBookRequest, ReturnBookRequest, TransactionStatus, TransactionType } from '../models';
import { AuthService } from './auth.service';
import { BookService } from './book.service';
import { TransactionService } from './transaction.service';

describe('TransactionService', () => {
  let service: TransactionService;
  let authService: jasmine.SpyObj<AuthService>;
  let bookService: jasmine.SpyObj<BookService>;
  let mockLocalStorage: { [key: string]: string };

  beforeEach(() => {
    // Mock localStorage
    mockLocalStorage = {};
    spyOn(localStorage, 'getItem').and.callFake((key: string) => mockLocalStorage[key] || null);
    spyOn(localStorage, 'setItem').and.callFake((key: string, value: string) => {
      mockLocalStorage[key] = value;
    });

    const authSpy = jasmine.createSpyObj('AuthService', ['getAllUsers', 'updateUserBorrowedBooks']);
    const bookSpy = jasmine.createSpyObj('BookService', ['updateBookAvailability']);

    TestBed.configureTestingModule({
      providers: [
        TransactionService,
        { provide: AuthService, useValue: authSpy },
        { provide: BookService, useValue: bookSpy }
      ]
    });

    service = TestBed.inject(TransactionService);
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    bookService = TestBed.inject(BookService) as jasmine.SpyObj<BookService>;

    // Setup default mock responses
    authService.getAllUsers.and.returnValue([
      {
        id: '1',
        fullName: 'Test User',
        email: '<EMAIL>',
        password: 'password',
        role: 'user' as any,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        borrowedBooks: [],
        borrowLimit: 3
      }
    ]);

    bookService.updateBookAvailability.and.returnValue(of(true));
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should borrow a book successfully', () => {
    const borrowRequest: BorrowBookRequest = {
      userId: '1',
      bookId: '1'
    };

    service.borrowBook(borrowRequest).subscribe(response => {
      expect(response.success).toBe(true);
      expect(response.transaction).toBeTruthy();
      expect(response.transaction?.type).toBe(TransactionType.BORROW);
      expect(response.transaction?.status).toBe(TransactionStatus.ACTIVE);
    });

    expect(authService.updateUserBorrowedBooks).toHaveBeenCalledWith('1', '1', 'add');
    expect(bookService.updateBookAvailability).toHaveBeenCalledWith('1', -1);
  });

  it('should prevent borrowing when user reaches limit', () => {
    // Mock user with existing borrowed books at limit
    authService.getAllUsers.and.returnValue([
      {
        id: '1',
        fullName: 'Test User',
        email: '<EMAIL>',
        password: 'password',
        role: 'user' as any,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        borrowedBooks: ['1', '2', '3'], // At limit
        borrowLimit: 3
      }
    ]);

    // Add existing transactions to simulate borrowed books
    const existingTransactions = [
      {
        id: '1',
        userId: '1',
        bookId: '1',
        type: TransactionType.BORROW,
        borrowDate: new Date(),
        dueDate: new Date(),
        status: TransactionStatus.ACTIVE,
        fine: 0
      },
      {
        id: '2',
        userId: '1',
        bookId: '2',
        type: TransactionType.BORROW,
        borrowDate: new Date(),
        dueDate: new Date(),
        status: TransactionStatus.ACTIVE,
        fine: 0
      },
      {
        id: '3',
        userId: '1',
        bookId: '3',
        type: TransactionType.BORROW,
        borrowDate: new Date(),
        dueDate: new Date(),
        status: TransactionStatus.ACTIVE,
        fine: 0
      }
    ];

    // Manually set transactions to simulate existing borrowed books
    (service as any).transactions = existingTransactions;

    const borrowRequest: BorrowBookRequest = {
      userId: '1',
      bookId: '4'
    };

    service.borrowBook(borrowRequest).subscribe(response => {
      expect(response.success).toBe(false);
      expect(response.message).toContain('Borrow limit reached');
    });
  });

  it('should return a book successfully', () => {
    // Setup existing transaction
    const existingTransaction = {
      id: '1',
      userId: '1',
      bookId: '1',
      type: TransactionType.BORROW,
      borrowDate: new Date(),
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      status: TransactionStatus.ACTIVE,
      fine: 0
    };

    (service as any).transactions = [existingTransaction];

    const returnRequest: ReturnBookRequest = {
      transactionId: '1',
      returnDate: new Date()
    };

    service.returnBook(returnRequest).subscribe(response => {
      expect(response.success).toBe(true);
      expect(response.message).toContain('returned successfully');
    });

    expect(authService.updateUserBorrowedBooks).toHaveBeenCalledWith('1', '1', 'remove');
    expect(bookService.updateBookAvailability).toHaveBeenCalledWith('1', 1);
  });

  it('should calculate fine for overdue books', () => {
    // Setup overdue transaction
    const overdueDate = new Date();
    overdueDate.setDate(overdueDate.getDate() - 5); // 5 days ago

    const overdueTransaction = {
      id: '1',
      userId: '1',
      bookId: '1',
      type: TransactionType.BORROW,
      borrowDate: new Date(),
      dueDate: overdueDate,
      status: TransactionStatus.ACTIVE,
      fine: 0
    };

    (service as any).transactions = [overdueTransaction];

    const returnRequest: ReturnBookRequest = {
      transactionId: '1',
      returnDate: new Date()
    };

    service.returnBook(returnRequest).subscribe(response => {
      expect(response.success).toBe(true);
      expect(response.fine).toBeGreaterThan(0);
      expect(response.message).toContain('fine');
    });
  });

  it('should get user borrowed books', () => {
    const transactions = [
      {
        id: '1',
        userId: '1',
        bookId: '1',
        type: TransactionType.BORROW,
        borrowDate: new Date(),
        dueDate: new Date(),
        status: TransactionStatus.ACTIVE,
        fine: 0
      },
      {
        id: '2',
        userId: '2',
        bookId: '2',
        type: TransactionType.BORROW,
        borrowDate: new Date(),
        dueDate: new Date(),
        status: TransactionStatus.ACTIVE,
        fine: 0
      }
    ];

    (service as any).transactions = transactions;

    service.getUserBorrowedBooks('1').subscribe(userBooks => {
      expect(userBooks.length).toBe(1);
      expect(userBooks[0].userId).toBe('1');
      expect(userBooks[0].status).toBe(TransactionStatus.ACTIVE);
    });
  });

  it('should get overdue transactions', () => {
    const overdueDate = new Date();
    overdueDate.setDate(overdueDate.getDate() - 1); // Yesterday

    const transactions = [
      {
        id: '1',
        userId: '1',
        bookId: '1',
        type: TransactionType.BORROW,
        borrowDate: new Date(),
        dueDate: overdueDate,
        status: TransactionStatus.ACTIVE,
        fine: 0
      },
      {
        id: '2',
        userId: '1',
        bookId: '2',
        type: TransactionType.BORROW,
        borrowDate: new Date(),
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Future date
        status: TransactionStatus.ACTIVE,
        fine: 0
      }
    ];

    (service as any).transactions = transactions;

    service.getOverdueTransactions().subscribe(overdueBooks => {
      expect(overdueBooks.length).toBe(1);
      expect(overdueBooks[0].id).toBe('1');
    });
  });

  it('should persist transactions to localStorage', () => {
    const borrowRequest: BorrowBookRequest = {
      userId: '1',
      bookId: '1'
    };

    service.borrowBook(borrowRequest).subscribe(() => {
      expect(localStorage.setItem).toHaveBeenCalledWith('library_transactions', jasmine.any(String));
    });
  });
});
