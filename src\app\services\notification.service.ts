import { Injectable } from '@angular/core';
import { MatSnackBar, MatSnackBarConfig } from '@angular/material/snack-bar';

export interface NotificationConfig {
  message: string;
  action?: string;
  duration?: number;
  type?: 'success' | 'error' | 'warning' | 'info';
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  private defaultConfig: MatSnackBarConfig = {
    duration: 3000,
    horizontalPosition: 'right',
    verticalPosition: 'top'
  };

  constructor(private snackBar: MatSnackBar) { }

  // Show success notification
  showSuccess(message: string, action?: string, duration?: number): void {
    this.show({
      message,
      action,
      duration,
      type: 'success'
    });
  }

  // Show error notification
  showError(message: string, action?: string, duration?: number): void {
    this.show({
      message,
      action,
      duration: duration || 5000, // Errors stay longer
      type: 'error'
    });
  }

  // Show warning notification
  showWarning(message: string, action?: string, duration?: number): void {
    this.show({
      message,
      action,
      duration,
      type: 'warning'
    });
  }

  // Show info notification
  showInfo(message: string, action?: string, duration?: number): void {
    this.show({
      message,
      action,
      duration,
      type: 'info'
    });
  }

  // Generic show method
  show(config: NotificationConfig): void {
    const snackBarConfig: MatSnackBarConfig = {
      ...this.defaultConfig,
      duration: config.duration || this.defaultConfig.duration,
      panelClass: this.getPanelClass(config.type)
    };

    this.snackBar.open(
      config.message,
      config.action || 'Close',
      snackBarConfig
    );
  }

  // Show notification with custom action
  showWithAction(message: string, action: string, callback: () => void, type?: 'success' | 'error' | 'warning' | 'info'): void {
    const snackBarConfig: MatSnackBarConfig = {
      ...this.defaultConfig,
      duration: 0, // Don't auto-dismiss when there's an action
      panelClass: this.getPanelClass(type)
    };

    const snackBarRef = this.snackBar.open(message, action, snackBarConfig);
    
    snackBarRef.onAction().subscribe(() => {
      callback();
    });
  }

  // Dismiss all notifications
  dismiss(): void {
    this.snackBar.dismiss();
  }

  private getPanelClass(type?: 'success' | 'error' | 'warning' | 'info'): string[] {
    const baseClass = 'notification-snackbar';
    
    switch (type) {
      case 'success':
        return [baseClass, 'success-snackbar'];
      case 'error':
        return [baseClass, 'error-snackbar'];
      case 'warning':
        return [baseClass, 'warning-snackbar'];
      case 'info':
        return [baseClass, 'info-snackbar'];
      default:
        return [baseClass];
    }
  }
}
