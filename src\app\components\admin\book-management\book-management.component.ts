import { CommonModule } from '@angular/common';
import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTableModule } from '@angular/material/table';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { Book, BookGenre, CreateBookRequest, UpdateBookRequest } from '../../../models';
import { BookService } from '../../../services/book.service';
import { NotificationService } from '../../../services/notification.service';
import { ValidationService } from '../../../services/validation.service';

@Component({
  selector: 'app-book-management',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatPaginatorModule,
    MatSelectModule,
    MatSnackBarModule,
    MatTableModule,
    MatToolbarModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './book-management.component.html',
  styleUrl: './book-management.component.scss'
})
export class BookManagementComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  books: Book[] = [];
  filteredBooks: Book[] = [];
  isLoading = false;
  isAddingBook = false;
  editingBook: Book | null = null;
  bookForm: FormGroup;
  searchForm: FormGroup;
  displayedColumns = ['title', 'author', 'genre', 'isbn', 'totalCopies', 'availableCopies', 'status', 'actions'];
  genres = Object.values(BookGenre);

  constructor(
    private bookService: BookService,
    private fb: FormBuilder,
    private notificationService: NotificationService,
    private dialog: MatDialog,
    private router: Router
  ) {
    this.bookForm = this.createBookForm();
    this.searchForm = this.createSearchForm();
  }

  ngOnInit(): void {
    this.subscribeToBookUpdates();
    this.setupSearchForm();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createBookForm(): FormGroup {
    const currentYear = new Date().getFullYear();
    const minDate = new Date(1800, 0, 1); // January 1, 1800
    const maxDate = new Date(currentYear + 1, 11, 31); // December 31, next year

    return this.fb.group({
      title: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(200),
        ValidationService.noWhitespaceValidator()
      ]],
      author: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(100),
        ValidationService.noWhitespaceValidator()
      ]],
      isbn: ['', [
        Validators.required,
        ValidationService.isbnValidator()
      ]],
      genre: ['', Validators.required],
      description: ['', [
        Validators.required,
        Validators.minLength(10),
        Validators.maxLength(1000),
        ValidationService.noWhitespaceValidator()
      ]],
      totalCopies: [1, [
        Validators.required,
        Validators.min(1),
        Validators.max(1000)
      ]],
      publishedDate: ['', [
        Validators.required,
        ValidationService.dateRangeValidator(minDate, maxDate)
      ]]
    });
  }

  private createSearchForm(): FormGroup {
    return this.fb.group({
      title: [''],
      author: [''],
      genre: [''],
      status: ['all']
    });
  }

  private setupSearchForm(): void {
    this.searchForm.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.filterBooks();
      });
  }

  private subscribeToBookUpdates(): void {
    this.isLoading = true;

    // Subscribe to real-time book updates
    this.bookService.books$
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (books) => {
          console.log('Admin component received book updates:', books.length, 'books');
          this.books = [...books]; // Create new array reference
          this.filterBooks();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading books:', error);
          this.notificationService.showError('Error loading books');
          this.isLoading = false;
        }
      });

    // Initial load to trigger the observable
    this.bookService.getAllBooks()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          console.log('Initial book load completed');
        },
        error: (error) => {
          console.error('Error in initial book load:', error);
        }
      });
  }

  private filterBooks(): void {
    const filters = this.searchForm.value;
    this.filteredBooks = this.books.filter(book => {
      const matchesTitle = !filters.title ||
        book.title.toLowerCase().includes(filters.title.toLowerCase());
      const matchesAuthor = !filters.author ||
        book.author.toLowerCase().includes(filters.author.toLowerCase());
      const matchesGenre = !filters.genre || book.genre === filters.genre;
      const matchesStatus = filters.status === 'all' ||
        (filters.status === 'active' && book.isActive) ||
        (filters.status === 'inactive' && !book.isActive) ||
        (filters.status === 'available' && book.availableCopies > 0) ||
        (filters.status === 'unavailable' && book.availableCopies === 0);

      return matchesTitle && matchesAuthor && matchesGenre && matchesStatus;
    });
  }

  showAddForm(): void {
    this.isAddingBook = true;
    this.editingBook = null;
    this.bookForm.reset();
  }

  hideAddForm(): void {
    this.isAddingBook = false;
    this.editingBook = null;
    this.bookForm.reset();
  }

  editBook(book: Book): void {
    this.editingBook = book;
    this.isAddingBook = true;
    this.bookForm.patchValue({
      title: book.title,
      author: book.author,
      isbn: book.isbn,
      genre: book.genre,
      description: book.description,
      totalCopies: book.totalCopies,
      publishedDate: book.publishedDate
    });
  }

  onSubmit(): void {
    if (this.bookForm.valid) {
      const formData = this.bookForm.value;

      // Additional validation
      if (!this.validateFormData(formData)) {
        return;
      }

      if (this.editingBook) {
        this.updateBook(formData);
      } else {
        this.addBook(formData);
      }
    } else {
      ValidationService.markFormGroupTouched(this.bookForm);
      this.notificationService.showError('Please fix the form errors before submitting');
    }
  }

  private validateFormData(formData: any): boolean {
    // Check for duplicate ISBN
    const existingBook = this.books.find(book =>
      book.isbn === formData.isbn &&
      (!this.editingBook || book.id !== this.editingBook.id)
    );

    if (existingBook) {
      this.notificationService.showError('A book with this ISBN already exists');
      return false;
    }

    // Validate published date is not in the future
    const publishedDate = new Date(formData.publishedDate);
    const today = new Date();
    if (publishedDate > today) {
      this.notificationService.showError('Published date cannot be in the future');
      return false;
    }

    return true;
  }

  private addBook(bookData: CreateBookRequest): void {
    this.isLoading = true;
    this.bookService.addBook(bookData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (newBook) => {
          this.books.push(newBook);
          this.filterBooks();
          this.hideAddForm();
          this.notificationService.showSuccess('Book added successfully!');
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error adding book:', error);
          this.notificationService.showError('Error adding book');
          this.isLoading = false;
        }
      });
  }

  private updateBook(bookData: UpdateBookRequest): void {
    if (!this.editingBook) return;

    this.isLoading = true;
    this.bookService.updateBook(this.editingBook.id, bookData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedBook) => {
          if (updatedBook) {
            const index = this.books.findIndex(b => b.id === updatedBook.id);
            if (index !== -1) {
              this.books[index] = updatedBook;
              this.filterBooks();
            }
          }
          this.hideAddForm();
          this.notificationService.showSuccess('Book updated successfully!');
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error updating book:', error);
          this.notificationService.showError('Error updating book');
          this.isLoading = false;
        }
      });
  }

  deleteBook(book: Book): void {
    if (confirm(`Are you sure you want to delete "${book.title}"?`)) {
      this.isLoading = true;
      this.bookService.deleteBook(book.id)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (success) => {
            if (success) {
              const index = this.books.findIndex(b => b.id === book.id);
              if (index !== -1) {
                this.books[index].isActive = false;
                this.filterBooks();
              }
              this.notificationService.showSuccess('Book deleted successfully!');
            }
            this.isLoading = false;
          },
          error: (error) => {
            console.error('Error deleting book:', error);
            this.notificationService.showError('Error deleting book');
            this.isLoading = false;
          }
        });
    }
  }

  toggleBookStatus(book: Book): void {
    const newStatus = !book.isActive;
    this.bookService.updateBook(book.id, { isActive: newStatus })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (updatedBook) => {
          if (updatedBook) {
            const index = this.books.findIndex(b => b.id === updatedBook.id);
            if (index !== -1) {
              this.books[index] = updatedBook;
              this.filterBooks();
            }
          }
          this.notificationService.showSuccess(
            `Book ${newStatus ? 'activated' : 'deactivated'} successfully!`
          );
        },
        error: (error) => {
          console.error('Error updating book status:', error);
          this.notificationService.showError('Error updating book status');
        }
      });
  }

  clearSearch(): void {
    this.searchForm.reset({
      title: '',
      author: '',
      genre: '',
      status: 'all'
    });
  }

  // Form validation helper methods
  getFieldError(fieldName: string): string {
    const control = this.bookForm.get(fieldName);
    return control ? ValidationService.getErrorMessage(control, fieldName) : '';
  }

  hasFieldError(fieldName: string): boolean {
    const control = this.bookForm.get(fieldName);
    return control ? ValidationService.hasErrors(control) : false;
  }

  isFieldValid(fieldName: string): boolean {
    const control = this.bookForm.get(fieldName);
    return control ? control.valid && (control.dirty || control.touched) : false;
  }

  isFieldInvalid(fieldName: string): boolean {
    const control = this.bookForm.get(fieldName);
    return control ? control.invalid && (control.dirty || control.touched) : false;
  }

  goBack(): void {
    this.router.navigate(['/dashboard']);
  }
}
