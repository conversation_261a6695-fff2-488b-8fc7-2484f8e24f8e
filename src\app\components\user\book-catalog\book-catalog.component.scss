// Page Toolbar
.page-toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .mat-toolbar {
    background: transparent;
  }
}

.toolbar-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.3rem;
  font-weight: 600;
  color: white;

  mat-icon {
    font-size: 1.5rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}

.spacer {
  flex: 1 1 auto;
}

.book-count {
  font-size: 0.95rem;
  opacity: 0.9;
  margin-right: 16px;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
  padding: 6px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  font-weight: 500;
}

// Main Container
.book-catalog-container {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 64px);
}

// Cards
.search-card,
.status-card,
.pagination-card {
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

// Search Card
.search-card {
  .mat-mdc-card-header {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
    border-radius: 16px 16px 0 0;
    margin: -1px -1px 0 -1px;
    padding: 24px;

    .mat-mdc-card-title {
      color: white;
      font-size: 1.25rem;
      font-weight: 600;
    }

    .mat-mdc-card-subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 0.95rem;
    }
  }
}

// Search Form
.search-form {
  padding: 8px 0;

  .search-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    align-items: end;
  }

  .search-field {
    width: 100%;

    .mat-mdc-form-field {
      .mat-mdc-text-field-wrapper {
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
      }

      &.mat-focused .mat-mdc-text-field-wrapper {
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
    }
  }

  .clear-button {
    height: 56px;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 12px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(238, 90, 36, 0.4);
    }
  }
}

// Status Card
.status-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="60" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    opacity: 0.3;
  }

  .status-info {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    position: relative;
    z-index: 1;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 25px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }

    &.warning {
      background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
      border: 1px solid rgba(255, 155, 86, 0.5);
      animation: pulse 2s ease-in-out infinite;
    }

    mat-icon {
      font-size: 22px;
      width: 22px;
      height: 22px;
    }

    span {
      font-size: 0.95rem;
      font-weight: 600;
    }
  }
}

// Loading Container
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px;
  gap: 24px;

  .mat-mdc-progress-spinner {
    --mdc-circular-progress-active-indicator-color: #667eea;
  }

  p {
    color: #666;
    font-size: 1.1rem;
    font-weight: 500;
  }
}

// Books Section
.books-section {
  margin-bottom: 32px;
}

// Books Grid
.books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 32px;
  margin-bottom: 32px;
}

.book-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: 1;
  }

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);

    &::before {
      height: 6px;
      background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    }
  }

  &.unavailable {
    opacity: 0.8;
    filter: grayscale(0.3);

    &::before {
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    }

    .book-title {
      color: #666;
    }
  }

  .mat-mdc-card-header {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    padding: 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .book-title {
    font-size: 1.2rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 6px;
    color: #333;
    transition: color 0.3s ease;
  }

  .book-author {
    color: #667eea;
    font-style: italic;
    font-weight: 500;
    font-size: 0.95rem;
  }

  .mat-mdc-card-content {
    padding: 24px;
  }

  .book-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .book-meta {
    mat-chip-set {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
    }

    .genre-chip {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      font-weight: 600;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }

    .availability-chip {
      font-weight: 600;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      &.available {
        background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
        animation: pulse 2s ease-in-out infinite;
      }

      &.unavailable {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
      }
    }
  }

  .book-description {
    color: #555;
    line-height: 1.6;
    margin: 0;
    font-size: 0.95rem;
    background: rgba(102, 126, 234, 0.02);
    padding: 16px;
    border-radius: 12px;
    border-left: 4px solid #667eea;
  }

  .book-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: rgba(255, 255, 255, 0.8);
    padding: 16px;
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);

    .stat-item {
      display: flex;
      align-items: center;
      gap: 12px;
      color: #555;
      font-size: 0.9rem;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        color: #667eea;
        transform: translateX(4px);
      }

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
        color: #667eea;
        background: rgba(102, 126, 234, 0.1);
        padding: 4px;
        border-radius: 50%;
      }
    }
  }

  .book-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    margin-top: auto;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);

    .borrow-button {
      flex: 1;
      margin-right: 12px;
      display: flex;
      align-items: center;
      gap: 10px;
      border-radius: 12px;
      padding: 12px 20px;
      font-weight: 600;
      font-size: 0.95rem;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &[color="primary"] {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);

        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
      }

      &[color="basic"] {
        background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
        color: white;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
      }
    }

    .details-button {
      flex-shrink: 0;
      width: 48px;
      height: 48px;
      border-radius: 12px;
      background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
      color: white;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 25px rgba(17, 153, 142, 0.4);
      }
    }
  }
}

// Pagination Card
.pagination-card {
  .mat-mdc-card-content {
    display: flex;
    justify-content: center;
  }
}

// No Data State
.no-data {
  text-align: center;
  padding: 64px 24px;
  color: #999;

  mat-icon {
    font-size: 80px;
    width: 80px;
    height: 80px;
    margin-bottom: 24px;
    opacity: 0.5;
  }

  h3 {
    margin: 0 0 16px 0;
    color: #666;
    font-weight: 400;
    font-size: 1.5rem;
  }

  p {
    margin: 0 0 24px 0;
    font-size: 1rem;
    line-height: 1.5;
  }

  button {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 auto;
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .book-catalog-container {
    padding: 16px;
  }

  .books-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .page-toolbar {
    .toolbar-title {
      font-size: 1rem;
    }

    .book-count {
      display: none;
    }

    button[mat-raised-button] {
      span {
        display: none;
      }
    }
  }

  .search-form .search-row {
    grid-template-columns: 1fr;
  }

  .status-info {
    flex-direction: column;
    align-items: stretch;

    .status-item {
      justify-content: center;
    }
  }

  .books-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .book-card {
    .book-stats {
      .stat-item {
        font-size: 0.8rem;

        mat-icon {
          font-size: 14px;
          width: 14px;
          height: 14px;
        }
      }
    }

    .book-actions {
      flex-direction: column;
      gap: 8px;

      .borrow-button {
        width: 100%;
        margin-right: 0;
      }
    }
  }
}

@media (max-width: 480px) {
  .book-catalog-container {
    padding: 8px;
  }

  .search-card,
  .status-card,
  .pagination-card {
    margin-bottom: 16px;
  }

  .book-card {
    .book-title {
      font-size: 1rem;
    }

    .book-description {
      font-size: 0.9rem;
    }

    .book-meta mat-chip {
      font-size: 0.7rem;
    }
  }

  .no-data {
    padding: 32px 16px;

    mat-icon {
      font-size: 60px;
      width: 60px;
      height: 60px;
    }

    h3 {
      font-size: 1.2rem;
    }

    p {
      font-size: 0.9rem;
    }
  }
}

// Animation and Effects
.book-card {
  &:hover .book-title {
    color: #1976d2;
  }
}

.borrow-button {
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: scale(1.02);
  }
}

.search-card,
.status-card,
.pagination-card {
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// Loading state for borrow button
.borrow-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}