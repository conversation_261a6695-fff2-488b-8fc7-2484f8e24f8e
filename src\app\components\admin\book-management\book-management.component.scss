// Page Toolbar
.page-toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);

  .mat-toolbar {
    background: transparent;
  }
}

.toolbar-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.3rem;
  font-weight: 600;
  color: white;

  mat-icon {
    font-size: 1.5rem;
    width: 1.5rem;
    height: 1.5rem;
  }
}

.spacer {
  flex: 1 1 auto;
}

.book-management-container {
  padding: 32px;
  max-width: 1400px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 64px);
}

.search-card,
.form-card,
.table-card {
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

.search-card {
  .mat-mdc-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 16px 16px 0 0;
    margin: -1px -1px 0 -1px;
    padding: 24px;

    .mat-mdc-card-title {
      color: white;
      font-size: 1.25rem;
      font-weight: 600;
    }
  }
}

.search-form {
  .search-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    align-items: end;
    padding: 8px 0;
  }

  .search-field {
    width: 100%;

    .mat-mdc-form-field {
      .mat-mdc-text-field-wrapper {
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
      }

      &.mat-focused .mat-mdc-text-field-wrapper {
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .clear-button {
    height: 56px;
    display: flex;
    align-items: center;
    gap: 8px;
    border-radius: 12px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(238, 90, 36, 0.4);
    }
  }
}

.form-card {
  .mat-mdc-card-header {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    color: white;
    border-radius: 16px 16px 0 0;
    margin: -1px -1px 0 -1px;
    padding: 24px;

    .mat-mdc-card-title {
      color: white;
      font-size: 1.25rem;
      font-weight: 600;
    }
  }
}

.book-form {
  padding: 8px 0;

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
  }

  .form-field {
    width: 100%;

    .mat-mdc-form-field {
      .mat-mdc-text-field-wrapper {
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
      }

      &.mat-focused .mat-mdc-text-field-wrapper {
        background: rgba(255, 255, 255, 1);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }
    }
  }

  .full-width {
    width: 100%;
    margin-bottom: 20px;

    .mat-mdc-form-field {
      .mat-mdc-text-field-wrapper {
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
      }
    }
  }

  .form-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);

    button {
      display: flex;
      align-items: center;
      gap: 8px;
      border-radius: 12px;
      padding: 12px 24px;
      font-weight: 600;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &.mat-mdc-raised-button {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
      }

      &.mat-mdc-button {
        color: #666;

        &:hover {
          background: rgba(0, 0, 0, 0.05);
        }
      }
    }
  }
}

.table-card {
  .mat-mdc-card-header {
    background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
    color: white;
    border-radius: 16px 16px 0 0;
    margin: -1px -1px 0 -1px;
    padding: 24px;

    .mat-mdc-card-title {
      color: white;
      font-size: 1.25rem;
      font-weight: 600;
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px;
  gap: 20px;

  .mat-mdc-progress-spinner {
    --mdc-circular-progress-active-indicator-color: #667eea;
  }

  p {
    color: #666;
    font-size: 1.1rem;
    font-weight: 500;
  }
}

.table-container {
  overflow-x: auto;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.books-table {
  width: 100%;
  min-width: 800px;
  background: transparent;

  .mat-mdc-header-cell {
    font-weight: 700;
    color: #333;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #667eea;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .mat-mdc-cell {
    padding: 16px 12px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .mat-mdc-row {
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
      transform: scale(1.01);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
  }

  .book-title {
    display: flex;
    flex-direction: column;
    gap: 6px;

    strong {
      color: #333;
      font-weight: 600;
      font-size: 1rem;
      line-height: 1.2;
    }

    .book-description {
      color: #666;
      font-size: 0.875rem;
      line-height: 1.4;
      opacity: 0.8;
    }
  }

  .genre-badge {
    display: inline-block;
    padding: 6px 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  }

  .isbn {
    font-family: 'JetBrains Mono', 'Courier New', monospace;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 6px 10px;
    border-radius: 8px;
    font-size: 0.85rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #495057;
    font-weight: 500;
  }

  .availability {
    font-weight: 700;
    font-size: 1.1rem;
    padding: 4px 8px;
    border-radius: 8px;
    display: inline-block;
    min-width: 30px;
    text-align: center;

    &.available {
      color: #27ae60;
      background: rgba(39, 174, 96, 0.1);
      border: 1px solid rgba(39, 174, 96, 0.3);
    }

    &.unavailable {
      color: #e74c3c;
      background: rgba(231, 76, 60, 0.1);
      border: 1px solid rgba(231, 76, 60, 0.3);
    }
  }

  .status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &.active {
      background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
      color: white;
      box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
    }

    &.inactive {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: white;
      box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;

    button {
      width: 40px;
      height: 40px;
      border-radius: 12px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &.mat-mdc-icon-button {
        &[color="primary"] {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;

          &:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
          }
        }

        &[color="accent"] {
          background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
          color: white;

          &:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(17, 153, 142, 0.4);
          }
        }

        &[color="warn"] {
          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
          color: white;

          &:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
          }
        }
      }
    }
  }
}

.no-data {
  text-align: center;
  padding: 64px 32px;
  color: #666;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
  border-radius: 16px;
  backdrop-filter: blur(10px);

  mat-icon {
    font-size: 80px;
    width: 80px;
    height: 80px;
    margin-bottom: 24px;
    opacity: 0.6;
    color: #667eea;
  }

  h3 {
    margin: 0 0 12px 0;
    color: #333;
    font-weight: 600;
    font-size: 1.5rem;
  }

  p {
    margin: 0;
    font-size: 1rem;
    opacity: 0.8;
    line-height: 1.5;
  }
}

// Responsive Design
@media (max-width: 1200px) {
  .book-management-container {
    padding: 24px 16px;
  }

  .books-table {
    min-width: 700px;
  }
}

@media (max-width: 768px) {
  .page-toolbar {
    .toolbar-title {
      font-size: 1.1rem;

      mat-icon {
        font-size: 1.3rem;
        width: 1.3rem;
        height: 1.3rem;
      }
    }
  }

  .book-management-container {
    padding: 16px 12px;
  }

  .search-form .search-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .book-form .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .books-table {
    min-width: 600px;
    font-size: 0.9rem;

    .mat-mdc-cell {
      padding: 12px 6px;
    }

    .book-title {
      .book-description {
        display: none;
      }
    }

    .action-buttons {
      flex-direction: column;
      gap: 4px;

      button {
        width: 36px;
        height: 36px;
      }
    }
  }
}

@media (max-width: 480px) {
  .book-management-container {
    padding: 12px 8px;
  }

  .search-card,
  .form-card,
  .table-card {
    margin-bottom: 20px;
    border-radius: 12px;
  }

  .books-table {
    min-width: 500px;
    font-size: 0.85rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 12px;

    button {
      width: 100%;
      justify-content: center;
      padding: 14px 20px;
    }
  }

  .no-data {
    padding: 48px 20px;

    mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
    }

    h3 {
      font-size: 1.25rem;
    }
  }
}

// Enhanced Animations and Effects
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.search-card,
.form-card,
.table-card {
  animation: fadeInUp 0.6s ease-out;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
}

.books-table {
  .mat-mdc-row {
    animation: fadeInUp 0.4s ease-out;

    &:nth-child(even) { animation-delay: 0.05s; }
    &:nth-child(odd) { animation-delay: 0.1s; }
  }
}

// Loading shimmer effect
.loading-container {
  .mat-mdc-progress-spinner {
    animation: pulse 2s ease-in-out infinite;
  }
}

// Hover effects for interactive elements
.search-field,
.form-field {
  .mat-mdc-form-field {
    &:hover .mat-mdc-text-field-wrapper {
      transform: translateY(-1px);
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    }
  }
}

// Button hover animations
button {
  &.mat-mdc-raised-button,
  &.mat-mdc-icon-button {
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }

    &:hover::before {
      left: 100%;
    }
  }
}

// Availability indicator animation
.availability {
  position: relative;

  &.available {
    animation: pulse 2s ease-in-out infinite;
  }

  &.unavailable {
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, transparent 40%, rgba(231, 76, 60, 0.1) 50%, transparent 60%);
      animation: shimmer 2s ease-in-out infinite;
    }
  }
}

// Status badge animations
.status-badge,
.genre-badge {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-1px) scale(1.05);
  }
}