/* You can add global styles to this file, and also import other style files */

html, body { height: 100%; }
body { margin: 0; font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif; }

/* Notification Snackbar Styles */
.notification-snackbar {
  font-weight: 500;
}

.success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}

.error-snackbar {
  background-color: #f44336 !important;
  color: white !important;
}

.warning-snackbar {
  background-color: #ff9800 !important;
  color: white !important;
}

.info-snackbar {
  background-color: #2196f3 !important;
  color: white !important;
}

.notification-snackbar .mat-mdc-snack-bar-action {
  color: white !important;
  font-weight: 600;
}

/* Global styles for Library Management System */
.full-width {
  width: 100%;
}

.spacer {
  flex: 1 1 auto;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: #666;
}

.no-data mat-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  margin-bottom: 1rem;
  color: #ccc;
}

/* Login and Register containers */
.login-container,
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card,
.register-card {
  width: 100%;
  max-width: 400px;
  padding: 1rem;
}

.demo-credentials {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 0.9rem;
}

/* Dashboard styles */
.dashboard-container {
  padding: 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 2rem;
}

.stats-grid,
.user-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card,
.user-stat-card {
  min-height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.stat-info h3 {
  margin: 0;
  font-size: 2rem;
  font-weight: bold;
}

.stat-info p {
  margin: 0;
  color: #666;
}

.quick-actions {
  margin-bottom: 2rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Status badges */
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.active {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.status-badge.returned {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-badge.overdue {
  background-color: #ffebee;
  color: #d32f2f;
}

/* Warning styles */
.warning {
  color: #f57c00 !important;
}

.warning .stat-icon {
  background-color: #fff3e0;
  color: #f57c00;
}

/* Table styles */
.table-container {
  overflow-x: auto;
}

.overdue-row {
  background-color: #ffebee;
}

.due-date.overdue,
.days-left.overdue {
  color: #d32f2f;
  font-weight: bold;
}

/* Book grid styles */
.books-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.book-card {
  transition: transform 0.2s ease-in-out;
}

.book-card:hover {
  transform: translateY(-2px);
}

.book-card.unavailable {
  opacity: 0.7;
}

/* Responsive design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 0.5rem;
  }

  .stats-grid,
  .user-stats-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
  }

  .books-grid {
    grid-template-columns: 1fr;
  }
}
